鲲谱智能 用户协议

欢迎使用鲲谱智能桌面 AI 客户端工具 KunAvatar。请您在使用本软件前，仔细阅读以下条款。继续使用本软件即表示您已完全理解并同意本协议的所有内容。


许可协议

本软件依据 Apache License 2.0 许可协议进行授权。除 Apache License 2.0 中已有条款外，使用鲲谱智能软件时，您还应遵循以下附加条款：

一. 商业使用许可

1. 免费商用：用户可在未修改源代码的前提下，将本软件用于商业目的，无需支付费用。

2. 商业授权要求：在以下任一情形下，用户需获得商业授权：
    a. 对本软件进行二次开发、修改或衍生（包括但不限于修改应用名称、标识、代码或功能）。
    b. 向企业客户提供支持 10 人或以上用户的多租户服务。
    c. 将本软件预装或集成至硬件设备或产品中并进行捆绑销售。
    d. 在涉及安全性、数据隐私等敏感需求时，向政府或教育机构提供大规模采购服务。

二. 贡献者协议

作为鲲谱智能的贡献者，您同意遵守以下条款：

1. 许可调整：鲲谱智能开发团队保留根据需要对开源协议进行调整的权利，调整内容可能使协议条款更加严格或宽松。

2. 商业使用：您贡献的代码可能会被用于商业用途，包括但不限于云端服务或相关商业业务。

三. 其他条款

1. 本协议的解释权归鲲谱智能开发团队所有。

2. 本协议条款可能会根据实际情况进行更新，更新时，用户将通过本软件收到通知。

若您有任何问题或需要申请商业授权，欢迎联系鲲谱智能开发团队。

除上述特定条款外，本软件的所有权利和限制均依据 Apache License 2.0 进行管理。有关 Apache License 2.0 的详细信息，请访问：http://www.apache.org/licenses/LICENSE-2.0。

本软件根据 Apache License 2.0 版授权（以下简称“许可证”），您在使用本软件时，必须遵守许可证的条款。您可以在以下网址查阅完整的许可证内容：http://www.apache.org/licenses/LICENSE-2.0

除非适用法律要求或获得书面许可，否则本软件在 许可证 约定下将以“原样”形式分发，不附带任何明示或暗示的保证或条件。请详细阅读许可证中的相关条款和限制。

-------------------------------------------------------------------------------------------------------------


KunpuAI User Agreement

Welcome to use KunpuAI, the desktop AI client tool by KunAvatar. Before using this software, please read the following terms carefully. By continuing to use this software, you indicate that you have fully understood and agreed to all the contents of this agreement.


License Agreement

This software is licensed under the Apache License 2.0. In addition to the terms already specified in the Apache License 2.0, when using the Kunpu Intelligent software, you must also comply with the following additional terms:


I. Commercial Use License

1. Free Commercial Use:Users may use this software for commercial purposes without modifying the source code and without paying any fees.

2. Commercial Authorization Requirements:Users must obtain commercial authorization in any of the following circumstances:
    a.Conduct secondary development, modification, or derivation of this software (including but not limited to modifying the application name, logo, code, or functions).

    b.Provide multi-tenant services to enterprise clients supporting 10 or more users.

    c.Pre-install or integrate this software into hardware devices or products for bundled sales.

    d.Provide large-scale procurement services to government or educational institutions when involving sensitive requirements such as security and data privacy.

II. Contributor Agreement

As a contributor to Kunpu Intelligent, you agree to comply with the following terms:

1. License Adjustment:The Kunpu Intelligent development team reserves the right to adjust the open-source license as needed, which may make the license terms stricter or more lenient.

2. Commercial Use:The code you contribute may be used for commercial purposes, including but not limited to cloud services or related commercial businesses.


III. Other Terms

1. The right to interpret this agreement belongs to the Kunpu Intelligent development team.

2. The terms of this agreement may be updated according to actual circumstances, and users will be notified through this software when updates occur.

If you have any questions or need to apply for commercial authorization, please feel free to contact the Kunpu Intelligent development team.

Except for the specific terms mentioned above, all rights and restrictions of this software are governed by the Apache License 2.0. For more details about the Apache License 2.0, please visit: http://www.apache.org/licenses/LICENSE-2.0.

This software is licensed under the Apache License, Version 2.0 (hereinafter referred to as the "License"); you must comply with the terms of the License when using this software. You can view the full License at: http://www.apache.org/licenses/LICENSE-2.0.

Unless required by applicable law or agreed to in writing, this software is distributed under the License on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. Please read the License for the specific language governing permissions and limitations under the License.
