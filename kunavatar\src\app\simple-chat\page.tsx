'use client';

import React, { useRef, Suspense, useEffect, useCallback } from 'react';
import {
  useSimpleChatStore,
  useMessageSender,
  useConversationManager,
  useModelManager,
  useAgentManager,
  useInitialization
} from './store';
import { Sidebar } from '../Sidebar';
import { ChatContainer } from './components';
import { PageLoading } from '../../components/Loading';
import { ProtectedRoute } from '@/components/ProtectedRoute';

// 内部组件，使用新的 store 架构
function SimpleChatPageContent() {
  // 🏪 使用统一的 store
  const {
    messages,
    inputMessage,
    isStreaming,
    selectedModel,
    currentConversation,
    error,
    isInitializing,
    enableTools,
    selectedTools,
    expandedThinkingMessages,
    selectedAgentId,
    selectorMode,
    isAgentMode,
    setInputMessage,
    setError,
    toggleThinkingExpand,
    setEnableTools,
    setSelectedTools,
  } = useSimpleChatStore();

  // � 业务逻辑 hooks（只保留5个核心hooks）
  const { sendMessage, stopGeneration, clearCurrentChat, insertText } = useMessageSender();
  const { conversations, createConversation, switchConversation, deleteConversation } = useConversationManager();
  const { models, handleModelChange } = useModelManager();
  const { agents, selectedAgent, handleAgentChange } = useAgentManager();
  const { initialize } = useInitialization();

  // 🚀 初始化 - 确保在认证完成后再初始化
  useEffect(() => {
    // 延迟初始化，确保 ProtectedRoute 的认证检查完成
    const timer = setTimeout(() => {
      initialize();
    }, 100);

    return () => clearTimeout(timer);
  }, []); // 移除 initialize 依赖，只在组件挂载时执行一次

  // 简化的创建对话处理函数
  const handleCreateConversation = useCallback(async () => {
    try {
      const conversationId = await createConversation({
        model: selectedModel || undefined,
        agentId: selectedAgentId || undefined
      });

      if (conversationId && typeof window !== 'undefined') {
        window.history.pushState(null, '', `/simple-chat?id=${conversationId}`);
      }
    } catch (err) {
      console.error('创建对话失败:', err);
      setError('创建对话失败');
    }
  }, [createConversation, selectedModel, selectedAgentId, setError]);

  // 模型切换处理器
  const onModelChange = useCallback((modelName: string) => {
    handleModelChange(modelName);
  }, [handleModelChange]);

  // Agent选择处理器
  const onAgentChange = useCallback((agentId: number | null) => {
    handleAgentChange(agentId);
  }, [handleAgentChange]);

  // 错误处理
  const dismissError = useCallback(() => {
    setError(null);
  }, [setError]);

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-theme-background-secondary dark:bg-theme-background overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar conversations={conversations} />

        {/* 主聊天区域 */}
        {isInitializing ? (
          <div className="flex-1 overflow-auto">
            <PageLoading text="loading" fullScreen={true} />
          </div>
        ) : (
          <ChatContainer
            currentConversation={currentConversation}
            models={models}
            selectedModel={selectedModel}
            onModelChange={onModelChange}
            agents={agents}
            selectedAgentId={selectedAgentId}
            onAgentChange={onAgentChange}
            selectorMode={selectorMode}
            onSelectorModeChange={(_mode) => {}} // 临时空函数，后续通过store处理
            isAgentMode={isAgentMode}
            customModels={models} // 使用models作为customModels
            messages={messages}
            inputMessage={inputMessage}
            onInputChange={setInputMessage}
            onSendMessage={sendMessage}
            isStreaming={isStreaming}
            onStopGeneration={stopGeneration}
            expandedThinkingMessages={expandedThinkingMessages}
            onToggleThinkingExpand={toggleThinkingExpand}
            enableTools={enableTools}
            selectedTools={selectedTools}
            onToolsToggle={setEnableTools}
            onSelectedToolsChange={setSelectedTools}
            onInsertText={insertText}
            onClearChat={clearCurrentChat}
            error={error}
            onDismissError={dismissError}
            onCreateConversation={handleCreateConversation}
            onCreateNewConversation={handleCreateConversation}
          />
        )}
      </div>
    </ProtectedRoute>
  );
}

// 外部组件，用Suspense包装内部组件
export default function SimpleChatPage() {
  return (
    <Suspense fallback={
      <div className="flex h-screen bg-background">
        <PageLoading text="loading" fullScreen={true} />
      </div>
    }>
      <SimpleChatPageContent />
    </Suspense>
  );
}