{"mcpServers": {"local": {"type": "stdio", "command": "npx", "args": ["tsx", "src/lib/mcp/mcp-server.ts"], "description": "本地硬编码MCP工具服务器，包含计算器、时间、文件操作等基础工具", "enabled": true}, "Exa Search": {"type": "streamable-http", "url": "https://server.smithery.ai/exa/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18&profile=close-aphid-tzkXG3", "path": "/", "protocol": "http", "description": "Fast, intelligent web search and crawling.\nExa combines embeddings and traditional search to deliver the best results for LLMs."}}}