'use client';

import { useCallback, useEffect } from 'react';
import { useChatStore } from './chatStore';
import { Model<PERSON><PERSON>e<PERSON><PERSON><PERSON>, AgentChangeHandler } from '../types';

// 模型管理业务逻辑
export const useModelManager = () => {
  const {
    models,
    selectedModel,
    setModels,
    setSelectedModel,
    setError,
  } = useChatStore();

  const loadModels = useCallback(async () => {
    try {
      const response = await fetch('/api/models');
      const data = await response.json();
      
      if (data.success) {
        setModels(data.models || []);
      } else {
        setError('加载模型列表失败');
      }
    } catch (error) {
      setError('网络错误，加载模型列表失败');
      console.error('加载模型失败:', error);
    }
  }, []);

  const selectBestModel = useCallback(() => {
    if (models.length > 0 && !selectedModel) {
      // 选择第一个可用模型
      setSelectedModel(models[0].base_model);
    }
  }, [models, selectedModel]);

  const handleModelChange: ModelChangeHandler = useCallback((modelName: string) => {
    setSelectedModel(modelName);
    setError(null);

    // 如果有当前对话，更新对话的模型
    const { currentConversation } = useChatStore.getState();
    if (currentConversation?.id) {
      const token = localStorage.getItem('accessToken');
      fetch(`/api/conversations/${currentConversation.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ model: modelName }),
      }).catch(error => {
        console.error('更新对话模型失败:', error);
      });
    }
  }, []);

  // 初始化时加载模型
  useEffect(() => {
    loadModels();
  }, [loadModels]);

  return {
    models,
    selectedModel,
    loadModels,
    selectBestModel,
    handleModelChange,
    isAgentMode: false, // 这个值将由智能体管理器控制
  };
};

// 智能体管理业务逻辑
export const useAgentManager = () => {
  const {
    agents,
    selectedAgentId,
    selectorMode,
    isAgentMode,
    currentConversation,
    setAgents,
    setSelectedAgentId,
    setSelectorMode,
    setIsAgentMode,
    setSelectedModel,
    setEnableTools,
    setSelectedTools,
    setError,
  } = useChatStore();

  const loadAgents = useCallback(async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/agents', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success) {
        setAgents(data.agents || []);
      } else {
        setError('加载智能体列表失败');
      }
    } catch (error) {
      setError('网络错误，加载智能体列表失败');
      console.error('加载智能体失败:', error);
    }
  }, []);

  const selectAgent = useCallback(async (agentId: number | null, conversationId?: string) => {
    try {
      setSelectedAgentId(agentId);
      
      if (agentId) {
        const agent = agents.find(a => a.id === agentId);
        if (agent) {
          // 设置智能体模式
          setIsAgentMode(true);
          setSelectorMode('agent');
          
          // 应用智能体配置
          if (agent.model) {
            setSelectedModel(agent.model.base_model);
          }
          
          // 配置工具
          if (agent.tools && agent.tools.length > 0) {
            setEnableTools(true);
            setSelectedTools(agent.tools.map(tool => tool.name));
          } else {
            setEnableTools(false);
            setSelectedTools([]);
          }
          
          // 如果有对话ID，更新对话的智能体关联
          if (conversationId) {
            const token = localStorage.getItem('accessToken');
            await fetch(`/api/conversations/${conversationId}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify({ agent_id: agentId }),
            });
          }
        }
      } else {
        // 取消智能体选择
        setIsAgentMode(false);
        setSelectorMode('model');
        setEnableTools(false);
        setSelectedTools([]);
        
        // 如果有对话ID，清除对话的智能体关联
        if (conversationId) {
          const token = localStorage.getItem('accessToken');
          await fetch(`/api/conversations/${conversationId}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({ agent_id: null }),
          });
        }
      }
    } catch (error) {
      setError('切换智能体失败');
      console.error('切换智能体失败:', error);
    }
  }, [agents]);

  const handleAgentChange: AgentChangeHandler = useCallback((agentId: number | null) => {
    selectAgent(agentId, currentConversation?.id);
  }, [selectAgent, currentConversation]);

  // 根据当前对话初始化智能体
  useEffect(() => {
    if (currentConversation?.agent_id && currentConversation.agent_id !== selectedAgentId) {
      selectAgent(currentConversation.agent_id);
    }
  }, [currentConversation?.agent_id, selectedAgentId, selectAgent]);

  // 初始化时加载智能体
  useEffect(() => {
    loadAgents();
  }, [loadAgents]);

  const selectedAgent = selectedAgentId ? agents.find(a => a.id === selectedAgentId) : null;

  return {
    agents,
    selectedAgentId,
    selectedAgent,
    selectorMode,
    isAgentMode,
    loadAgents,
    selectAgent,
    handleAgentChange,
  };
};

// URL处理业务逻辑（简化版）
export const useUrlHandler = () => {
  const { isProcessingUrl } = useChatStore();

  // 简化的URL处理，主要逻辑移到初始化中
  const processUrlParams = useCallback(async () => {
    // URL处理逻辑已简化，主要在页面加载时处理
    console.log('URL参数处理已简化');
  }, []);

  return {
    isProcessingUrl,
    processUrlParams,
  };
};

// 初始化管理业务逻辑
export const useInitialization = () => {
  const {
    hasMinimumLoadTime,
    isInitializing,
    setIsInitializing,
    setHasMinimumLoadTime,
  } = useChatStore();

  // 直接使用内部函数避免循环依赖
  const loadConversationsInternal = useCallback(async () => {
    try {
      useChatStore.getState().setConversationLoading(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/conversations', {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      const data = await response.json();
      if (data.success) {
        useChatStore.getState().setConversations(data.conversations || []);
      }
    } catch (error) {
      console.error('加载对话失败:', error);
    } finally {
      useChatStore.getState().setConversationLoading(false);
    }
  }, []);

  const { loadModels, selectBestModel } = useModelManager();
  const { loadAgents } = useAgentManager();

  const initialize = useCallback(async () => {
    try {
      setIsInitializing(true);

      // 设置最小加载时间
      setTimeout(() => {
        setHasMinimumLoadTime(true);
      }, 1000);

      // 并行加载数据
      await Promise.all([
        loadModels(),
        loadAgents(),
        loadConversationsInternal(),
      ]);

      // 选择最佳模型
      selectBestModel();

      // 等待最小加载时间
      if (!hasMinimumLoadTime) {
        await new Promise(resolve => {
          const checkInterval = setInterval(() => {
            if (useChatStore.getState().hasMinimumLoadTime) {
              clearInterval(checkInterval);
              resolve(void 0);
            }
          }, 100);
        });
      }

    } catch (error) {
      console.error('初始化失败:', error);
    } finally {
      setIsInitializing(false);
    }
  }, [hasMinimumLoadTime, loadModels, loadAgents, loadConversationsInternal, selectBestModel]);

  return {
    isInitializing,
    initialize,
  };
};


