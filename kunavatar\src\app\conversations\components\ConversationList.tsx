import React from 'react';
import { MessageSquare, Trash2, Calendar, Clock, Bot, CheckSquare, Square, Hash, Type } from 'lucide-react';
import { motion } from 'framer-motion';
import { Conversation } from '@/lib/database';

interface ConversationListProps {
  conversations: Conversation[];
  onEnterConversation: (conversationId: string) => void;
  onDeleteConversation: (conversation: Conversation) => void;
  isSelectionMode?: boolean;
  selectedConversations?: Set<string>;
  onToggleSelection?: (conversationId: string) => void;
}

export function ConversationList({
  conversations,
  onEnterConversation,
  onDeleteConversation,
  isSelectionMode = false,
  selectedConversations = new Set(),
  onToggleSelection,
}: ConversationListProps) {
  const formatDate = (dateString: string) => {
    // SQLite DATETIME 是 UTC 时间，需要正确解析
    const date = new Date(dateString + 'Z'); // 添加 Z 表示 UTC 时间
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const formatUpdateTime = (dateString: string) => {
    // SQLite DATETIME 是 UTC 时间，需要正确解析
    const date = new Date(dateString + 'Z'); // 添加 Z 表示 UTC 时间
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-md)' }}>
      {conversations.map((conversation, index) => (
        <motion.div
          key={conversation.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05, duration: 0.3 }}
          className="group transition-all duration-300 cursor-pointer"
          style={{
            backgroundColor: 'var(--color-card)',
            border: `1px solid ${
              isSelectionMode && selectedConversations.has(conversation.id) 
                ? 'var(--color-primary)' 
                : 'var(--color-border)'
            }`,
            borderRadius: 'var(--radius-lg)',
            padding: 'var(--spacing-md)',
            boxShadow: isSelectionMode && selectedConversations.has(conversation.id) 
              ? `0 0 0 2px rgba(var(--color-primary-rgb), 0.1)` 
              : 'var(--shadow-sm)'
          }}
          onMouseEnter={(e) => {
            if (!isSelectionMode || !selectedConversations.has(conversation.id)) {
              e.currentTarget.style.boxShadow = 'var(--shadow-lg)';
              e.currentTarget.style.borderColor = 'rgba(var(--color-primary-rgb), 0.2)';
            }
          }}
          onMouseLeave={(e) => {
            if (isSelectionMode && selectedConversations.has(conversation.id)) {
              e.currentTarget.style.boxShadow = `0 0 0 2px rgba(var(--color-primary-rgb), 0.1)`;
              e.currentTarget.style.borderColor = 'var(--color-primary)';
            } else {
              e.currentTarget.style.boxShadow = 'var(--shadow-sm)';
              e.currentTarget.style.borderColor = 'var(--color-border)';
            }
          }}
                    onClick={() => onEnterConversation(conversation.id)}
        >
                    <div className="flex items-start justify-between">
            <div className="flex items-start" style={{ gap: 'var(--spacing-sm)', flex: 1, minWidth: 0 }}>
              {/* 选择框 */}
              {isSelectionMode && (
                <div 
                  className="flex-shrink-0 transition-colors duration-200"
                  style={{ marginTop: 'var(--spacing-xs)' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggleSelection?.(conversation.id);
                  }}
                >
                  {selectedConversations.has(conversation.id) ? (
                    <CheckSquare 
                      className="w-5 h-5 cursor-pointer" 
                      style={{ color: 'var(--color-primary)' }}
                    />
                  ) : (
                    <Square 
                      className="w-5 h-5 cursor-pointer" 
                      style={{ color: 'var(--color-foreground-muted)' }}
                    />
                  )}
                </div>
              )}
              
              <div className="flex-1 min-w-0">
                {/* 对话标题和图标 */}
                <div className="flex items-center" style={{ gap: 'var(--spacing-sm)', marginBottom: 'var(--spacing-xs)' }}>
                  <div style={{
                    padding: 'var(--spacing-xs)',
                    backgroundColor: 'rgba(var(--color-primary-rgb), 0.1)',
                    borderRadius: 'var(--radius-lg)'
                  }}>
                    <MessageSquare className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
                  </div>
                  <h3 className="card-title truncate">
                    {conversation.title}
                  </h3>
                </div>

                {/* 模型信息 */}
                <div className="flex items-center" style={{ gap: 'var(--spacing-xs)', marginBottom: 'var(--spacing-sm)' }}>
                  <Bot className="w-4 h-4" style={{ color: 'var(--color-foreground-muted)' }} />
                  <span style={{
                    fontSize: 'var(--font-size-sm)',
                    color: 'var(--color-foreground-muted)',
                    backgroundColor: 'var(--color-background)',
                    padding: `var(--spacing-xs) var(--spacing-xs)`,
                    borderRadius: 'var(--radius-md)'
                  }}>
                    {conversation.model}
                  </span>
                </div>

                {/* 统计信息 */}
                {conversation.stats && (
                  <div className="flex items-center flex-wrap" style={{ 
                    gap: 'var(--spacing-md)', 
                    fontSize: 'var(--font-size-sm)', 
                    color: 'var(--color-foreground-muted)',
                    marginBottom: 'var(--spacing-sm)'
                  }}>
                    <div className="flex items-center" style={{ gap: 'var(--spacing-xs)' }}>
                      <MessageSquare className="w-4 h-4" />
                      <span>{conversation.stats.message_count} 条消息</span>
                    </div>
                    <div className="flex items-center" style={{ gap: 'var(--spacing-xs)' }}>
                      <Hash className="w-4 h-4" />
                      <span>{conversation.stats.total_tokens.toLocaleString()} tokens</span>
                    </div>
                    <div className="flex items-center" style={{ gap: 'var(--spacing-xs)' }}>
                      <Type className="w-4 h-4" />
                      <span>{conversation.stats.total_characters.toLocaleString()} 字符</span>
                    </div>
                  </div>
                )}

                {/* 时间信息 */}
                <div className="flex items-center" style={{ 
                  gap: 'var(--spacing-md)', 
                  fontSize: 'var(--font-size-sm)', 
                  color: 'var(--color-foreground-muted)' 
                }}>
                  <div className="flex items-center" style={{ gap: 'var(--spacing-xs)' }}>
                    <Calendar className="w-4 h-4" />
                    <span>创建于 {formatDate(conversation.created_at)}</span>
                  </div>
                  <div className="flex items-center" style={{ gap: 'var(--spacing-xs)' }}>
                    <Clock className="w-4 h-4" />
                    <span>更新于 {formatUpdateTime(conversation.updated_at)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            {!isSelectionMode && (
              <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-200" style={{ gap: 'var(--spacing-xs)' }}>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteConversation(conversation);
                  }}
                  className="transition-all duration-200"
                  style={{
                    padding: 'var(--spacing-xs)',
                    color: 'var(--color-foreground-muted)',
                    borderRadius: 'var(--radius-lg)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = 'var(--color-error)';
                    e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = 'var(--color-foreground-muted)';
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                  title="删除对话"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

                      {/* 进入对话提示 */}
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200" style={{
              marginTop: 'var(--spacing-sm)',
              paddingTop: 'var(--spacing-sm)',
              borderTop: `1px solid var(--color-border)`
            }}>
              <p style={{
                fontSize: 'var(--font-size-sm)',
                color: 'var(--color-primary)',
                fontWeight: '500'
              }}>
                {isSelectionMode ? '点击选择对话' : '点击进入对话'} →
              </p>
            </div>
        </motion.div>
      ))}
    </div>
  );
}