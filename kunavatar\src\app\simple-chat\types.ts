export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  tool_calls?: any[];
  tool_call_id?: string;
  tool_name?: string; // 新增：用于tool角色消息标识执行的工具名称
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolCallResult {
  tool_call_id: string;
  content: string;
}

export type AIStatus = 'idle' | 'loading' | 'generating' | 'tool_calling' | 'thinking';

export interface AIState {
  status: AIStatus;
  message?: string;
  toolName?: string;
  progress?: number;
  thinkingStartTime?: number;
}

// 强化类型安全 - 具体的函数签名类型
export type MessageSetter = React.Dispatch<React.SetStateAction<SimpleMessage[]>>;
export type StringSetter = React.Dispatch<React.SetStateAction<string>>;
export type StringNullableSetter = React.Dispatch<React.SetStateAction<string | null>>;
export type BooleanSetter = React.Dispatch<React.SetStateAction<boolean>>;
export type ToolCallSetter = React.Dispatch<React.SetStateAction<RuntimeToolCall[]>>;
export type ToolCallRuntimeSetter = React.Dispatch<React.SetStateAction<RuntimeToolCall | null>>;
export type StringArraySetter = React.Dispatch<React.SetStateAction<string[]>>;
export type ErrorSetter = React.Dispatch<React.SetStateAction<string | null>>;
export type AbortControllerSetter = React.Dispatch<React.SetStateAction<AbortController | null>>;

// 事件处理函数类型
export type MessageHandler = (message: string) => void;
export type VoidHandler = () => void;
export type PromiseVoidHandler = () => Promise<void>;
export type ModelChangeHandler = (model: string) => void;
export type AgentChangeHandler = (agentId: number | null) => void;
export type ToolsChangeHandler = (tools: string[]) => void;
export type ThinkingToggleHandler = (messageId: string) => void;

// 更具体的业务函数类型
export type ConversationTitleUpdater = (id: string, title: string) => Promise<void>;
export type MessageDatabaseUpdater = (dbMessages: any[], setMessages: Function, setToolCalls: Function) => void;

// 统一的消息接口 - 同时支持 SimpleMessage 和 Message 的使用场景
export interface SimpleMessage {
  id: string;
  role: 'user' | 'assistant' | 'tool_call' | 'system' | 'tool' | 'tool_result';
  content: string;
  timestamp: number;
  model?: string;
  toolCall?: ToolCallRuntime;
  // 扩展字段 - 支持思考模式
  isThinking?: boolean;
  tool_calls?: any[];
  // 统计字段（可选）
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

// 统一的运行时工具调用接口
export interface ToolCallRuntime {
  id: string;
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}

// 类型别名 - 为了向后兼容和语义清晰
export type Message = SimpleMessage;
export type RuntimeToolCall = ToolCallRuntime;