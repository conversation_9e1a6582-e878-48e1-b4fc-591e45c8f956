'use client';

import { useCallback, useEffect, useRef } from 'react';
import { useChatStore } from './chatStore';
import { streamingChatService } from '../services/streamingChatService';
import { 
  <PERSON>oid<PERSON><PERSON><PERSON>, 
  PromiseVoidHandler, 
  <PERSON><PERSON><PERSON>eH<PERSON><PERSON>, 
  Agent<PERSON><PERSON>eHandler,
  MessageHandler,
  ThinkingToggleHandler
} from '../types';

// 消息发送业务逻辑
export const useMessageSender = () => {
  const {
    currentConversation,
    selectedModel,
    selectedAgentId,
    inputMessage,
    isStreaming,
    enableTools,
    selectedTools,
    messages,
    setMessages,
    setInputMessage,
    setIsStreaming,
    setError,
    setToolCalls,
    setActiveTool,
    setCurrentAssistantMessageId,
    setAbortController,
    addMessage,
  } = useChatStore();

  const sendMessage = useCallback(async () => {
    if (!inputMessage.trim() || isStreaming || !selectedModel) return;

    const userMessage = {
      id: `user-${Date.now()}`,
      role: 'user' as const,
      content: inputMessage.trim(),
      timestamp: Date.now(),
    };

    // 添加用户消息
    addMessage(userMessage);
    setInputMessage('');
    setIsStreaming(true);
    setError(null);

    try {
      const abortController = new AbortController();
      setAbortController(abortController);

      const assistantMessageId = `assistant-${Date.now()}`;
      const assistantMessage = {
        id: assistantMessageId,
        role: 'assistant' as const,
        content: '',
        timestamp: Date.now(),
        model: selectedModel,
      };

      addMessage(assistantMessage);
      setCurrentAssistantMessageId(assistantMessageId);

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [...messages, userMessage],
          conversationId: currentConversation?.id,
          agentId: selectedAgentId,
          stream: true,
          enableTools,
          selectedTools,
        }),
        signal: abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 处理流式响应
      await streamingChatService.processStreamingResponse(
        response,
        {
          onMessageUpdate: (messageId, content, stats) => {
            useChatStore.getState().updateMessage(messageId, { content, ...stats });
          },
          onToolCallStart: (toolCall) => {
            useChatStore.getState().setActiveTool(toolCall);
            useChatStore.getState().addToolCall(toolCall);
            
            const toolCallMessage = {
              id: `tool-runtime-${toolCall.id}`,
              role: 'tool_call' as const,
              content: '',
              timestamp: Date.now(),
              toolCall: toolCall,
            };
            useChatStore.getState().addMessage(toolCallMessage);
          },
          onToolCallComplete: (toolCallId, toolName, result, executionTime) => {
            useChatStore.getState().setActiveTool(null);
            useChatStore.getState().updateToolCall(toolCallId, {
              status: 'completed',
              result,
              executionTime,
            });

            // 创建新的助手消息用于工具调用后的回复
            const newAssistantMessageId = `assistant-post-tool-${toolName}-${Date.now()}`;
            const newAssistantMessage = {
              id: newAssistantMessageId,
              role: 'assistant' as const,
              content: '',
              timestamp: Date.now(),
              model: selectedModel,
            };
            useChatStore.getState().addMessage(newAssistantMessage);
            useChatStore.getState().setCurrentAssistantMessageId(newAssistantMessageId);
          },
          onToolCallError: (toolCallId, toolName, error, executionTime) => {
            useChatStore.getState().setActiveTool(null);
            useChatStore.getState().updateToolCall(toolCallId, {
              status: 'error',
              error,
              executionTime,
            });
          },
          onNewAssistantMessage: (messageId) => {
            useChatStore.getState().setCurrentAssistantMessageId(messageId);
          },
          onTitleUpdate: (conversationId, title) => {
            useChatStore.getState().updateConversation(conversationId, { title });
          },
          onStreamEnd: () => {
            setIsStreaming(false);
            setAbortController(null);
          },
          onError: (error) => {
            setError(error);
            setIsStreaming(false);
            setAbortController(null);
          },
        },
        assistantMessageId,
        abortController
      );
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        setError(error.message || '发送消息失败');
      }
      setIsStreaming(false);
      setAbortController(null);
    }
  }, [
    currentConversation,
    selectedModel,
    selectedAgentId,
    inputMessage,
    isStreaming,
    enableTools,
    selectedTools,
    messages,
  ]);

  const stopGeneration = useCallback(() => {
    const { abortController } = useChatStore.getState();
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
    setIsStreaming(false);
  }, []);

  const clearCurrentChat = useCallback(() => {
    useChatStore.getState().resetChatState();
    useChatStore.getState().resetToolState();
  }, []);

  const insertText = useCallback((text: string) => {
    const { inputMessage } = useChatStore.getState();
    setInputMessage(inputMessage + text);
  }, []);

  return {
    sendMessage,
    stopGeneration,
    clearCurrentChat,
    insertText,
  };
};

// 对话管理业务逻辑
export const useConversationManager = () => {
  const {
    conversations,
    currentConversation,
    conversationLoading,
    error,
    setConversations,
    addConversation,
    updateConversation,
    removeConversation,
    setCurrentConversation,
    setConversationLoading,
    setError,
  } = useChatStore();

  const loadConversations = useCallback(async () => {
    try {
      setConversationLoading(true);
      setError(null);

      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/conversations', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success) {
        setConversations(data.conversations || []);
      } else {
        setError(data.error || '加载对话列表失败');
      }
    } catch (err) {
      setError('网络错误，加载对话列表失败');
      console.error('加载对话列表失败:', err);
    } finally {
      setConversationLoading(false);
    }
  }, []);

  const createConversation = useCallback(async (options: {
    title?: string;
    model?: string | null;
    agentId?: number | null;
  } = {}): Promise<string | null> => {
    try {
      setError(null);
      
      const requestBody = { 
        title: options.title || `新对话 ${new Date().toLocaleString()}`,
        model: options.model || null,
        agent_id: options.agentId || null,
      };

      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });
      
      const data = await response.json();
      
      if (data.success) {
        addConversation(data.conversation);
        setCurrentConversation(data.conversation);
        return data.conversation.id;
      } else {
        setError(data.error || '创建对话失败');
        return null;
      }
    } catch (err) {
      setError('网络错误，创建对话失败');
      return null;
    }
  }, []);

  const switchConversation = useCallback(async (id: string) => {
    try {
      setError(null);
      
      // 先检查是否已在conversations列表中有此对话的基本信息
      const existingConversation = conversations.find(conv => conv.id === id);
      
      if (existingConversation) {
        setCurrentConversation(existingConversation);
        return;
      }
      
      // 如果没有缓存，再发起API请求
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success) {
        setCurrentConversation(data.conversation);
        
        // 更新conversations列表中的对话信息
        const exists = conversations.find(conv => conv.id === id);
        if (!exists) {
          addConversation(data.conversation);
        } else {
          updateConversation(id, data.conversation);
        }
      } else {
        setError(data.error || '切换对话失败');
        throw new Error(data.error || '切换对话失败');
      }
    } catch (err) {
      setError('网络错误，切换对话失败');
      throw err;
    }
  }, [conversations]);

  const deleteConversation = useCallback(async (id: string) => {
    try {
      setError(null);
      
      if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {
        return;
      }
      
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 如果删除的是当前对话，清空当前对话
        if (currentConversation?.id === id) {
          setCurrentConversation(null);
        }
        // 从本地列表中移除
        removeConversation(id);
      } else {
        setError(data.error || '删除对话失败');
      }
    } catch (err) {
      setError('网络错误，删除对话失败');
      console.error('删除对话失败:', err);
    }
  }, [currentConversation]);

  return {
    conversations,
    currentConversation,
    loading: conversationLoading,
    error,
    loadConversations,
    createConversation,
    switchConversation,
    deleteConversation,
  };
};
