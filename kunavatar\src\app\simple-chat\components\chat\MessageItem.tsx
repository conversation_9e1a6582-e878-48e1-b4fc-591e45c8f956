'use client';

import React, { memo } from 'react';
import { Bo<PERSON>, User } from 'lucide-react';
import { ThinkingMode, hasThinkingContent, removeThinkingContent } from '../ui/ThinkingMode';
import StreamedContent from '../ui/StreamedContent';
import { ToolCallMessage } from '../tools/ToolCallMessage';
import { ChatStyle, DisplaySize } from '../input-controls';
import { SimpleMessage } from '../../types';
import ModelLogo from '@/app/model-manager/components/ModelLogo';
import { AgentWithRelations } from '@/app/agents/types';
import { AgentAvatar } from '../ui/AgentAvatar';

interface MessageItemProps {
  message: SimpleMessage;
  index: number;
  isStreaming: boolean;
  isLastMessage: boolean;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  chatStyle: ChatStyle;
  displaySize: DisplaySize;
  selectedModel?: string;
  customModels?: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  selectedAgent?: AgentWithRelations | null;
  renderGenerationStatsIcon: (message: SimpleMessage) => React.ReactNode;
  getModelDisplayInfo: (modelName?: string) => { displayName: string; family: string };
}

// 优化的单个消息项组件
const MessageItemComponent: React.FC<MessageItemProps> = ({
  message,
  index,
  isStreaming,
  isLastMessage,
  expandedThinkingMessages,
  onToggleThinkingExpand,
  chatStyle,
  displaySize,
  selectedModel,
  selectedAgent,
  renderGenerationStatsIcon,
  getModelDisplayInfo,
}) => {
  // 如果是工具调用占位符消息，渲染工具调用组件
  if (message.role === 'tool_call' && message.toolCall) {
    return (
      <ToolCallMessage key={message.id} toolCall={message.toolCall} />
    );
  }
  
  // 检查消息是否包含思考内容
  const hasThinking = message.role === 'assistant' && hasThinkingContent(message.content);
  const contentWithoutThinking = hasThinking ? removeThinkingContent(message.content) : message.content;
  const isCurrentlyThinking = isStreaming && message.role === 'assistant' && isLastMessage && hasThinkingContent(message.content) && !removeThinkingContent(message.content).trim();
  
  // 对于 assistant 消息，如果只有思考内容而没有实际内容，且不是正在生成状态，则不显示消息气泡
  const isGenerating = isStreaming && message.role === 'assistant' && isLastMessage;
  const hasActualContent = contentWithoutThinking.trim().length > 0;
  const shouldShowBubble = message.role === 'user' || hasActualContent || (isGenerating && !isCurrentlyThinking);
  
  // 获取模型显示信息
  const modelDisplayInfo = getModelDisplayInfo(message.model || selectedModel);
  
  // 根据聊天样式决定布局
  if (chatStyle === 'conversation') {
    // 对话模式：用户右侧，AI左侧
    const isUser = message.role === 'user';
    return (
      <div className={`flex ${displaySize === 'compact' ? 'gap-2' : 'gap-3'} ${isUser ? 'flex-row-reverse' : ''}`}>
        <div className={`${displaySize === 'compact' ? 'w-8 h-8' : 'w-10 h-10'} rounded-full flex items-center justify-center flex-shrink-0 ${
          isUser 
            ? 'bg-theme-primary text-white' 
            : 'bg-theme-card border border-theme-border text-theme-foreground'
        }`}>
          {isUser ? (
            <User className={displaySize === 'compact' ? 'w-4 h-4' : 'w-5 h-5'} />
          ) : (
            selectedAgent ? (
              <AgentAvatar 
                agent={selectedAgent}
                size={displaySize === 'compact' ? 'md' : 'lg'}
              />
            ) : (
              <ModelLogo 
                modelName={modelDisplayInfo.family}
                size={displaySize === 'compact' ? 'md' : 'lg'}
                containerSize={displaySize === 'compact' ? 32 : 40}
                imageSize={displaySize === 'compact' ? 24 : 32}
                className="bg-transparent border-0 rounded-full"
              />
            )
          )}
        </div>
        <div className={`max-w-[80%] ${displaySize === 'compact' ? 'space-y-1' : 'space-y-2'} ${isUser ? 'flex flex-col items-end' : ''}`}>
          {/* 只有在需要显示消息气泡或者有思考内容时才显示角色标识 */}
          {(shouldShowBubble || hasThinking || isCurrentlyThinking) && (
            <div className={`flex items-center gap-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
              <div className={`text-sm text-theme-foreground-muted ${isUser ? 'text-right' : 'text-left'}`}>
                {isUser ? '你' : (selectedAgent ? selectedAgent.name : modelDisplayInfo.displayName)}
              </div>
              {/* AI消息的统计信息 */}
              {!isUser && message.role === 'assistant' && renderGenerationStatsIcon(message)}
            </div>
          )}
          
          {/* 思考面板 - 只对AI消息显示 */}
          {message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && (
            <ThinkingMode
              content={message.content}
              isExpanded={expandedThinkingMessages.has(message.id)}
              onToggleExpand={() => onToggleThinkingExpand(message.id)}
              defaultHidden={true}
            />
          )}
          
          {/* 消息气泡 - 只有在应该显示时才渲染 */}
          {shouldShowBubble && (
            <div className={`inline-block ${displaySize === 'compact' ? 'p-2' : 'p-3'} rounded-lg ${
              isUser 
                ? 'bg-theme-primary text-white' 
                : 'text-theme-foreground'
            }`}>
              {isGenerating && !isCurrentlyThinking && !contentWithoutThinking ? (
                <div className="flex items-center gap-2">
                  <div className="spinner-small">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <span className="text-sm text-theme-foreground-muted">loading...</span>
                </div>
              ) : (
                <StreamedContent
                  content={contentWithoutThinking || ''}
                  isStreaming={isGenerating}
                  enableMarkdown={!isUser} // 重新启用：只对AI助手的消息启用markdown渲染
                  className={!isUser ? "break-words leading-[1.4]" : "break-words whitespace-pre-wrap leading-[1.4]"}
                  style={{
                    minWidth: 0,
                    maxWidth: '100%',
                  }}
                />
              )}
            </div>
          )}
        </div>
      </div>
    );
  } else {
    // 助手模式：所有消息都在左侧
    return (
      <div className={`flex ${displaySize === 'compact' ? 'gap-2' : 'gap-3'}`}>
        <div className={`${displaySize === 'compact' ? 'w-8 h-8' : 'w-10 h-10'} rounded-full flex items-center justify-center flex-shrink-0 ${
          message.role === 'user' 
            ? 'bg-theme-primary text-white' 
            : 'bg-theme-card border border-theme-border text-theme-foreground'
        }`}>
          {message.role === 'user' ? (
            <User className={displaySize === 'compact' ? 'w-4 h-4' : 'w-5 h-5'} />
          ) : (
            selectedAgent ? (
              <AgentAvatar 
                agent={selectedAgent}
                size={displaySize === 'compact' ? 'md' : 'lg'}
              />
            ) : (
              <ModelLogo 
                modelName={modelDisplayInfo.family}
                size={displaySize === 'compact' ? 'md' : 'lg'}
                containerSize={displaySize === 'compact' ? 32 : 40}
                imageSize={displaySize === 'compact' ? 24 : 32}
                className="bg-transparent border-0 rounded-full"
              />
            )
          )}
        </div>
        <div className={`flex-1 max-w-[80%] ${displaySize === 'compact' ? 'space-y-1' : 'space-y-2'}`}>
          {/* 角色标识和统计信息 */}
          {(shouldShowBubble || hasThinking || isCurrentlyThinking) && (
            <div className="flex items-center gap-2">
              <div className="text-sm text-theme-foreground-muted">
                {message.role === 'user' ? '你' : (selectedAgent ? selectedAgent.name : modelDisplayInfo.displayName)}
              </div>
              {/* AI消息的统计信息 */}
              {message.role === 'assistant' && renderGenerationStatsIcon(message)}
            </div>
          )}
          
          {/* 思考面板 - 只对AI消息显示 */}
          {message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && (
            <ThinkingMode
              content={message.content}
              isExpanded={expandedThinkingMessages.has(message.id)}
              onToggleExpand={() => onToggleThinkingExpand(message.id)}
              defaultHidden={true}
            />
          )}
          
          {/* 消息气泡 - 只有在应该显示时才渲染 */}
          {shouldShowBubble && (
            <div className={`inline-block ${displaySize === 'compact' ? 'p-2' : 'p-3'} rounded-lg ${
              message.role === 'user' 
                ? 'bg-theme-primary text-white' 
                : 'text-theme-foreground'
            }`}>
              {isGenerating && !isCurrentlyThinking && !contentWithoutThinking ? (
                <div className="flex items-center gap-2">
                  <div className="spinner-small">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <span className="text-sm text-theme-foreground-muted">loading...</span>
                </div>
              ) : (
                <StreamedContent
                  content={contentWithoutThinking || ''}
                  isStreaming={isGenerating}
                  enableMarkdown={message.role !== 'user'} // 重新启用：只对AI助手的消息启用markdown渲染
                  className={message.role !== 'user' ? "break-words leading-[1.4]" : "break-words whitespace-pre-wrap leading-[1.4]"}
                  style={{
                    minWidth: 0,
                    maxWidth: '100%',
                  }}
                />
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
};

// 使用 React.memo 优化单个消息项的性能
export const MessageItem = memo(MessageItemComponent, (prevProps, nextProps) => {
  // 只有消息内容、流式状态或思考展开状态变化时才重新渲染
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.isStreaming === nextProps.isStreaming &&
    prevProps.isLastMessage === nextProps.isLastMessage &&
    prevProps.expandedThinkingMessages.has(prevProps.message.id) === 
    nextProps.expandedThinkingMessages.has(nextProps.message.id) &&
    prevProps.chatStyle === nextProps.chatStyle &&
    prevProps.displaySize === nextProps.displaySize &&
    prevProps.selectedAgent?.id === nextProps.selectedAgent?.id
  );
});
