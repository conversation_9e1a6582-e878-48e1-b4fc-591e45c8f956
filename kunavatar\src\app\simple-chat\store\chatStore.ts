'use client';

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { SimpleMessage, RuntimeToolCall } from '../types';
import { Conversation } from '@/lib/database/types';
import { AgentWithRelations } from '@/app/agents/types';
import { CustomModel } from '@/lib/database/custom-models';

// 聊天状态接口
interface ChatState {
  // 消息相关状态
  messages: SimpleMessage[];
  inputMessage: string;
  isStreaming: boolean;
  expandedThinkingMessages: Set<string>;
  
  // 工具相关状态
  enableTools: boolean;
  selectedTools: string[];
  activeTool: RuntimeToolCall | null;
  toolCalls: RuntimeToolCall[];
  currentAssistantMessageId: string | null;
  
  // 模型和智能体状态
  selectedModel: string;
  models: CustomModel[];
  selectedAgentId: number | null;
  agents: AgentWithRelations[];
  selectorMode: 'model' | 'agent';
  isAgentMode: boolean;
  
  // 对话相关状态
  conversations: Conversation[];
  currentConversation: Conversation | null;
  conversationLoading: boolean;
  
  // UI状态
  error: string | null;
  isInitializing: boolean;
  hasMinimumLoadTime: boolean;
  isProcessingUrl: boolean;
  
  // 控制状态
  abortController: AbortController | null;
}

// 聊天操作接口
interface ChatActions {
  // 消息操作
  setMessages: (messages: SimpleMessage[]) => void;
  addMessage: (message: SimpleMessage) => void;
  updateMessage: (messageId: string, updates: Partial<SimpleMessage>) => void;
  setInputMessage: (message: string) => void;
  setIsStreaming: (streaming: boolean) => void;
  toggleThinkingExpand: (messageId: string) => void;
  clearMessages: () => void;
  
  // 工具操作
  setEnableTools: (enabled: boolean) => void;
  setSelectedTools: (tools: string[]) => void;
  setActiveTool: (tool: RuntimeToolCall | null) => void;
  setToolCalls: (toolCalls: RuntimeToolCall[]) => void;
  addToolCall: (toolCall: RuntimeToolCall) => void;
  updateToolCall: (toolCallId: string, updates: Partial<RuntimeToolCall>) => void;
  setCurrentAssistantMessageId: (id: string | null) => void;
  
  // 模型和智能体操作
  setSelectedModel: (model: string) => void;
  setModels: (models: CustomModel[]) => void;
  setSelectedAgentId: (agentId: number | null) => void;
  setAgents: (agents: AgentWithRelations[]) => void;
  setSelectorMode: (mode: 'model' | 'agent') => void;
  setIsAgentMode: (isAgent: boolean) => void;
  
  // 对话操作
  setConversations: (conversations: Conversation[]) => void;
  addConversation: (conversation: Conversation) => void;
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => void;
  removeConversation: (conversationId: string) => void;
  setCurrentConversation: (conversation: Conversation | null) => void;
  setConversationLoading: (loading: boolean) => void;
  
  // UI操作
  setError: (error: string | null) => void;
  setIsInitializing: (initializing: boolean) => void;
  setHasMinimumLoadTime: (hasTime: boolean) => void;
  setIsProcessingUrl: (processing: boolean) => void;
  
  // 控制操作
  setAbortController: (controller: AbortController | null) => void;
  
  // 复合操作
  resetChatState: () => void;
  resetToolState: () => void;
}

// 合并状态和操作
export type ChatStore = ChatState & ChatActions;

// 初始状态
const initialState: ChatState = {
  // 消息相关状态
  messages: [],
  inputMessage: '',
  isStreaming: false,
  expandedThinkingMessages: new Set(),
  
  // 工具相关状态
  enableTools: false,
  selectedTools: [],
  activeTool: null,
  toolCalls: [],
  currentAssistantMessageId: null,
  
  // 模型和智能体状态
  selectedModel: '',
  models: [],
  selectedAgentId: null,
  agents: [],
  selectorMode: 'model',
  isAgentMode: false,
  
  // 对话相关状态
  conversations: [],
  currentConversation: null,
  conversationLoading: false,
  
  // UI状态
  error: null,
  isInitializing: true,
  hasMinimumLoadTime: false,
  isProcessingUrl: false,
  
  // 控制状态
  abortController: null,
};

// 创建 Zustand store
export const useChatStore = create<ChatStore>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        ...initialState,
        
        // 消息操作
        setMessages: (messages) => set((state) => {
          state.messages = messages;
        }),
        
        addMessage: (message) => set((state) => {
          state.messages.push(message);
        }),
        
        updateMessage: (messageId, updates) => set((state) => {
          const index = state.messages.findIndex(msg => msg.id === messageId);
          if (index !== -1) {
            Object.assign(state.messages[index], updates);
          }
        }),
        
        setInputMessage: (message) => set((state) => {
          state.inputMessage = message;
        }),
        
        setIsStreaming: (streaming) => set((state) => {
          state.isStreaming = streaming;
        }),
        
        toggleThinkingExpand: (messageId) => set((state) => {
          if (state.expandedThinkingMessages.has(messageId)) {
            state.expandedThinkingMessages.delete(messageId);
          } else {
            state.expandedThinkingMessages.add(messageId);
          }
        }),
        
        clearMessages: () => set((state) => {
          state.messages = [];
        }),
        
        // 工具操作
        setEnableTools: (enabled) => set((state) => {
          state.enableTools = enabled;
        }),
        
        setSelectedTools: (tools) => set((state) => {
          state.selectedTools = tools;
        }),
        
        setActiveTool: (tool) => set((state) => {
          state.activeTool = tool;
        }),
        
        setToolCalls: (toolCalls) => set((state) => {
          state.toolCalls = toolCalls;
        }),
        
        addToolCall: (toolCall) => set((state) => {
          state.toolCalls.push(toolCall);
        }),
        
        updateToolCall: (toolCallId, updates) => set((state) => {
          const index = state.toolCalls.findIndex(tc => tc.id === toolCallId);
          if (index !== -1) {
            Object.assign(state.toolCalls[index], updates);
          }
        }),
        
        setCurrentAssistantMessageId: (id) => set((state) => {
          state.currentAssistantMessageId = id;
        }),
        
        // 模型和智能体操作
        setSelectedModel: (model) => set((state) => {
          state.selectedModel = model;
        }),
        
        setModels: (models) => set((state) => {
          state.models = models;
        }),
        
        setSelectedAgentId: (agentId) => set((state) => {
          state.selectedAgentId = agentId;
        }),
        
        setAgents: (agents) => set((state) => {
          state.agents = agents;
        }),
        
        setSelectorMode: (mode) => set((state) => {
          state.selectorMode = mode;
        }),
        
        setIsAgentMode: (isAgent) => set((state) => {
          state.isAgentMode = isAgent;
        }),
        
        // 对话操作
        setConversations: (conversations) => set((state) => {
          state.conversations = conversations;
        }),
        
        addConversation: (conversation) => set((state) => {
          state.conversations.unshift(conversation);
        }),
        
        updateConversation: (conversationId, updates) => set((state) => {
          const index = state.conversations.findIndex(conv => conv.id === conversationId);
          if (index !== -1) {
            Object.assign(state.conversations[index], updates);
          }
        }),
        
        removeConversation: (conversationId) => set((state) => {
          state.conversations = state.conversations.filter(conv => conv.id !== conversationId);
        }),
        
        setCurrentConversation: (conversation) => set((state) => {
          state.currentConversation = conversation;
        }),
        
        setConversationLoading: (loading) => set((state) => {
          state.conversationLoading = loading;
        }),
        
        // UI操作
        setError: (error) => set((state) => {
          state.error = error;
        }),
        
        setIsInitializing: (initializing) => set((state) => {
          state.isInitializing = initializing;
        }),
        
        setHasMinimumLoadTime: (hasTime) => set((state) => {
          state.hasMinimumLoadTime = hasTime;
        }),
        
        setIsProcessingUrl: (processing) => set((state) => {
          state.isProcessingUrl = processing;
        }),
        
        // 控制操作
        setAbortController: (controller) => set((state) => {
          state.abortController = controller;
        }),
        
        // 复合操作
        resetChatState: () => set((state) => {
          state.messages = [];
          state.inputMessage = '';
          state.isStreaming = false;
          state.expandedThinkingMessages = new Set();
          state.error = null;
        }),
        
        resetToolState: () => set((state) => {
          state.activeTool = null;
          state.toolCalls = [];
          state.currentAssistantMessageId = null;
        }),
      }))
    ),
    {
      name: 'chat-store',
    }
  )
);
