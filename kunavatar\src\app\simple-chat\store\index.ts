// 统一的 store 导出文件
export { useChatStore } from './chatStore';
export type { ChatStore } from './chatStore';

// 业务逻辑 hooks
export { 
  useMessageSender, 
  useConversationManager 
} from './businessHooks';

export { 
  useModelManager, 
  useAgentManager, 
  useUrlHandler, 
  useInitialization 
} from './modelAgentHooks';

// 简化的组合 hook，用于主页面组件
export const useSimpleChatStore = () => {
  const store = useChatStore();
  
  // 只返回主页面需要的状态和方法
  return {
    // 核心状态
    messages: store.messages,
    inputMessage: store.inputMessage,
    isStreaming: store.isStreaming,
    selectedModel: store.selectedModel,
    currentConversation: store.currentConversation,
    error: store.error,
    isInitializing: store.isInitializing,
    
    // 工具状态
    enableTools: store.enableTools,
    selectedTools: store.selectedTools,
    expandedThinkingMessages: store.expandedThinkingMessages,
    
    // 智能体状态
    selectedAgentId: store.selectedAgentId,
    selectorMode: store.selectorMode,
    isAgentMode: store.isAgentMode,
    
    // 基本操作
    setInputMessage: store.setInputMessage,
    setError: store.setError,
    toggleThinkingExpand: store.toggleThinkingExpand,
    
    // 工具操作
    setEnableTools: store.setEnableTools,
    setSelectedTools: store.setSelectedTools,
  };
};
